# dual_tabs_hybrid.py
import time
import sys
import threading
from dataclasses import dataclass
from typing import Tuple, List, Optional
import pyautogui

# ====================== GLOBAL CONFIG ======================
WAIT_BEFORE_START   = 4.0
SAFE_LOOP_SLEEP     = 0.05
STRIDE              = 1     # scan every Nth pixel (2 or 3 = faster, a tiny bit less precise)
BLACK_MAX           = 40    # r,g,b <= BLACK_MAX counts as black-ish
pyautogui.FAILSAFE  = True  # slam mouse to top-left to abort
# ===========================================================

# ---------------------- dataclasses ----------------------
@dataclass
class Rect:
    bl: <PERSON>ple[int, int]   # bottom-left (x, y)
    tr: Tuple[int, int]   # top-right  (x, y)

@dataclass
class LeftTabConfig:
    name: str
    refresh_pos: Tuple[int, int]
    post_refresh_delay: float          # wait after refresh before selecting majors
    cs1_pos: Tuple[int, int]
    cs2_pos: Tuple[int, int]
    cs_select_delay: float             # wait after clicking CS1/CS2 before checking
    rect1: Rect
    rect2: Rect
    enroll_pos: Tuple[int, int]
    pass_interval: float               # minimum seconds between full passes

@dataclass
class RightTabConfig:
    name: str
    refresh_positions: List[Tuple[int, int]]  # two courses: [courseA_refresh, courseB_refresh]
    post_refresh_delay: float
    watch_rect: Rect                          # same for both courses
    enroll_pos: Tuple[int, int]
    pass_interval: float

# ---------------------- FILL THESE ----------------------
LEFT_TAB = LeftTabConfig(
    name="Left",
    refresh_pos=(744, 429),             # TODO
    post_refresh_delay=0.20,            # tweak if the page is slow after refresh
    cs1_pos=(745, 502),                 # TODO: first CS button
    cs2_pos=(745, 525),                 # TODO: second CS button
    cs_select_delay=0.12,               # small delay after clicking CS before checking
    rect1=Rect(bl=(599, 511), tr= (658, 500)),  # TODO: first rectangle
    rect2=Rect(bl=(597, 534), tr=(658, 520)),  # TODO: second rectangle
    enroll_pos=(724, 683),              # TODO
    pass_interval=1.0,
)

RIGHT_TAB = RightTabConfig(
    name="Right",
    refresh_positions=[ (1706, 438), (1706, 474)],  # set both course refresh coords
    post_refresh_delay=0.30,                       # you used 0.3 earlier; keep/tune
    watch_rect=Rect(bl=(1572, 561), tr= (1626, 550)),
    enroll_pos=  (1684, 667),
    pass_interval=1.0,
)
# --------------------------------------------------------

# ---------------------- globals ----------------------
mouse_lock = threading.Lock()
stop_all = threading.Event()  # global abort (failsafe/Ctrl+C)
tab_done = {"Left": False, "Right": False}

# ---------------------- utils ----------------------
def norm_rect(r: Rect):
    x1, y1 = r.bl
    x2, y2 = r.tr
    left   = min(x1, x2)
    right  = max(x1, x2)
    top    = min(y1, y2)
    bottom = max(y1, y2)
    w = max(0, right - left)
    h = max(0, bottom - top)
    return left, top, w, h

def is_black(rgb) -> bool:
    r, g, b = rgb
    return r <= BLACK_MAX and g <= BLACK_MAX and b <= BLACK_MAX

def region_has_black(left: int, top: int, w: int, h: int) -> bool:
    if w == 0 or h == 0:
        return False
    with mouse_lock:
        img = pyautogui.screenshot(region=(left, top, w, h))
    for y in range(0, h, STRIDE):
        for x in range(0, w, STRIDE):
            if is_black(img.getpixel((x, y))):
                return True
    return False

def click(pos: Tuple[int,int], move_dur: float = 0.05, double: bool = False):
    with mouse_lock:
        pyautogui.moveTo(*pos, duration=move_dur)
        pyautogui.doubleClick() if double else pyautogui.click()

# ---------------------- workers ----------------------
def left_tab_worker(cfg: LeftTabConfig):
    l1, t1, w1, h1 = norm_rect(cfg.rect1)
    l2, t2, w2, h2 = norm_rect(cfg.rect2)
    last_pass = 0.0

    try:
        while not stop_all.is_set() and not tab_done[cfg.name]:
            now = time.time()
            if now - last_pass < cfg.pass_interval:
                time.sleep(SAFE_LOOP_SLEEP)
                continue

            # refresh once per pass
            click(cfg.refresh_pos)
            time.sleep(cfg.post_refresh_delay)

            # --- option 1: CS1 then Rect1 ---
            click(cfg.cs1_pos)
            time.sleep(cfg.cs_select_delay)
            if region_has_black(l1, t1, w1, h1):
                with mouse_lock:
                    if not stop_all.is_set() and not tab_done[cfg.name]:
                        print(f"\n[{cfg.name}] CS1/Rect1: seat detected — enrolling.")
                        pyautogui.moveTo(*cfg.enroll_pos, duration=0.08)
                        pyautogui.click()
                        print(f"[{cfg.name}] enroll clicked. left tab done.")
                        tab_done[cfg.name] = True
                if tab_done[cfg.name]:
                    break

            # --- option 2: CS2 then Rect2 ---
            click(cfg.cs2_pos)
            time.sleep(cfg.cs_select_delay)
            if region_has_black(l2, t2, w2, h2):
                with mouse_lock:
                    if not stop_all.is_set() and not tab_done[cfg.name]:
                        print(f"\n[{cfg.name}] CS2/Rect2: seat detected — enrolling.")
                        pyautogui.moveTo(*cfg.enroll_pos, duration=0.08)
                        pyautogui.click()
                        print(f"[{cfg.name}] enroll clicked. left tab done.")
                        tab_done[cfg.name] = True

            last_pass = time.time()
            time.sleep(SAFE_LOOP_SLEEP)

    except pyautogui.FailSafeException:
        print(f"\n[{cfg.name}] abort: failsafe corner.")
        stop_all.set()
    except KeyboardInterrupt:
        print(f"\n[{cfg.name}] abort: ctrl+c")
        stop_all.set()
    except Exception as e:
        print(f"\n[{cfg.name}] error: {e}", file=sys.stderr)
        tab_done[cfg.name] = True

def right_tab_worker(cfg: RightTabConfig):
    l, t, w, h = norm_rect(cfg.watch_rect)
    last_pass = 0.0

    try:
        while not stop_all.is_set() and not tab_done[cfg.name]:
            now = time.time()
            if now - last_pass < cfg.pass_interval:
                time.sleep(SAFE_LOOP_SLEEP)
                continue

            # pass over both courses
            for idx, refresh_pos in enumerate(cfg.refresh_positions):
                if stop_all.is_set() or tab_done[cfg.name]:
                    break
                click(refresh_pos)
                time.sleep(cfg.post_refresh_delay)
                if region_has_black(l, t, w, h):
                    with mouse_lock:
                        if not stop_all.is_set() and not tab_done[cfg.name]:
                            print(f"\n[{cfg.name}] Course #{idx+1}: seat detected — enrolling.")
                            pyautogui.moveTo(*cfg.enroll_pos, duration=0.08)
                            pyautogui.click()
                            print(f"[{cfg.name}] enroll clicked. right tab done.")
                            tab_done[cfg.name] = True
                    break

            last_pass = time.time()
            time.sleep(SAFE_LOOP_SLEEP)

    except pyautogui.FailSafeException:
        print(f"\n[{cfg.name}] abort: failsafe corner.")
        stop_all.set()
    except KeyboardInterrupt:
        print(f"\n[{cfg.name}] abort: ctrl+c")
        stop_all.set()
    except Exception as e:
        print(f"\n[{cfg.name}] error: {e}", file=sys.stderr)
        tab_done[cfg.name] = True

# ---------------------- main ----------------------
def main():
    print("failsafe: slam mouse to top-left corner to abort.")
    print(f"starting in {WAIT_BEFORE_START:.0f}s...")
    time.sleep(WAIT_BEFORE_START)

    t_left  = threading.Thread(target=left_tab_worker,  args=(LEFT_TAB,),  daemon=True)
    t_right = threading.Thread(target=right_tab_worker, args=(RIGHT_TAB,), daemon=True)
    t_left.start()
    t_right.start()

    try:
        while not stop_all.is_set() and not all(tab_done.values()):
            time.sleep(0.1)
    finally:
        t_left.join(timeout=1.0)
        t_right.join(timeout=1.0)
        if stop_all.is_set():
            print("\nSTOPPED (abort).")
        else:
            done_list = [name for name, done in tab_done.items() if done]
            print(f"\nDONE — completed tabs: {', '.join(done_list) if done_list else 'none'}")

if __name__ == "__main__":
    main()